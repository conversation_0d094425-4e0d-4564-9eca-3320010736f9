name: ci

on:
  release:
    types: [published]
  push:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.set-tag.outputs.IMAGE_TAG }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set image tag
        id: set-tag
        run: |
          if [[ "${{ github.event_name }}" == "push" ]]; then
            SHORT_SHA=$(git rev-parse --short HEAD)
            echo "IMAGE_TAG=v0.0.0-${SHORT_SHA}" >> $GITHUB_OUTPUT
          else
            # Validate that the tag only contains allowed characters
            if [[ ! "${{ github.event.release.tag_name }}" =~ ^v[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9_-]+)*$ ]]; then
              echo "Invalid tag name for Docker: ${{ github.event.release.tag_name }}"
              exit 1
            fi
            echo "IMAGE_TAG=${{ github.event.release.tag_name }}" >> $GITHUB_OUTPUT
          fi

  push-split:
    needs: setup
    if: needs.setup.result == 'success'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [api, gateway, ui, docs]
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push split
        uses: docker/build-push-action@v6
        with:
          context: .
          file: infra/split.dockerfile
          push: true
          tags: |
            ghcr.io/${{ github.repository }}-${{ matrix.app }}:${{ needs.setup.outputs.image_tag }}
            ghcr.io/${{ github.repository }}-${{ matrix.app }}:latest
          target: ${{ matrix.app }}
          cache-from: type=gha,scope=${{ matrix.app }}
          cache-to: type=gha,mode=max,scope=${{ matrix.app }}
          build-args: |
            APP_VERSION=${{ needs.setup.outputs.image_tag }}

  push-unified:
    needs: setup
    if: needs.setup.result == 'success'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push unified
        uses: docker/build-push-action@v6
        with:
          context: .
          file: infra/unified.dockerfile
          push: true
          platforms: linux/amd64,linux/arm64
          tags: |
            ghcr.io/${{ github.repository }}-unified:${{ needs.setup.outputs.image_tag }}
            ghcr.io/${{ github.repository }}-unified:latest
          cache-from: type=gha,scope=unified
          cache-to: type=gha,mode=max,scope=unified
          build-args: |
            APP_VERSION=${{ needs.setup.outputs.image_tag }}
