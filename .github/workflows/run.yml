name: prepare

on:
  workflow_call:
    inputs:
      cmd:
        required: true
        type: string

jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Collect versions
        run: |
          echo "nodejs_version=$(cat .tool-versions | grep 'nodejs' | cut -d ' ' -f 2)" >> "$GITHUB_ENV"
          echo "pnpm_version=$(cat .tool-versions | grep 'pnpm' | cut -d ' ' -f 2)" >> "$GITHUB_ENV"

      - uses: pnpm/action-setup@v4
        with:
          version: ${{ env.pnpm_version }}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.nodejs_version }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: ${{ inputs.cmd }}
        run: |
          set -eux
          ${{ inputs.cmd }}
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          KLUSTER_AI_API_KEY: ${{ secrets.KLUSTER_AI_API_KEY }}
          INFERENCE_NET_API_KEY: ${{ secrets.INFERENCE_NET_API_KEY }}
          TOGETHER_AI_API_KEY: ${{ secrets.TOGETHER_AI_API_KEY }}
          GOOGLE_AI_STUDIO_API_KEY: ${{ secrets.GOOGLE_AI_STUDIO_API_KEY }}
          CLOUD_RIFT_API_KEY: ${{ secrets.CLOUD_RIFT_API_KEY }}
          MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
          X_AI_API_KEY: ${{ secrets.X_AI_API_KEY }}
          GROQ_API_KEY: ${{ secrets.GROQ_API_KEY }}
          DEEPSEEK_API_KEY: ${{ secrets.DEEPSEEK_API_KEY }}
          PERPLEXITY_API_KEY: ${{ secrets.PERPLEXITY_API_KEY }}
