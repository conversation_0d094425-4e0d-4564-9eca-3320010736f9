{"extends": "../../tsconfig.json", "compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "types": ["vinxi/types/client", "vinxi/types/server", "node"], "paths": {"@/*": ["./src/*"], "@llmgateway/*": ["../../packages/*/src"], "content-collections": ["./.content-collections/generated"]}}, "include": ["./src/**/*.ts", "./src/**/*.tsx", "./src/types/**/*.d.ts", "./.content-collections/generated/index.d.ts"]}