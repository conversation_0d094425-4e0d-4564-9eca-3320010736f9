name: llmgateway-prod

services:
  # PostgreSQL Database
  postgres:
    platform: linux/amd64
    image: postgres:17-alpine
    container_name: llmgateway-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB:-llmgateway}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../packages/db/init:/docker-entrypoint-initdb.d
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - llmgateway-network

  # Redis for caching and queues
  redis:
    image: redis:8-alpine
    platform: linux/amd64
    container_name: llmgateway-redis
    restart: unless-stopped
    command:
      [
        "redis-server",
        "--appendonly",
        "yes",
        "--requirepass",
        "${REDIS_PASSWORD}",
      ]
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - llmgateway-network

  # Gateway Service
  gateway:
    build:
      context: ..
      dockerfile: infra/split.dockerfile
      target: gateway
    container_name: llmgateway-gateway
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=80
      - DATABASE_URL=postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-change_this_secure_password}@postgres:5432/${POSTGRES_DB:-llmgateway}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      # LLM Provider API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - VERTEX_API_KEY=${VERTEX_API_KEY}
      - GOOGLE_AI_STUDIO_API_KEY=${GOOGLE_AI_STUDIO_API_KEY}
      - INFERENCE_NET_API_KEY=${INFERENCE_NET_API_KEY}
      - KLUSTER_AI_API_KEY=${KLUSTER_AI_API_KEY}
      - TOGETHER_AI_API_KEY=${TOGETHER_AI_API_KEY}
    ports:
      - "${GATEWAY_PORT:-4001}:80"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:80/",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - llmgateway-network

  # API Service
  api:
    build:
      context: ..
      dockerfile: infra/split.dockerfile
      target: api
    container_name: llmgateway-api
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - RUN_MIGRATIONS=true
      - PORT=80
      - DATABASE_URL=postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-llmgateway}
      - UI_URL=${UI_URL:-http://localhost:3002}
      - API_URL=${API_URL:-http://localhost:4002}
      - ORIGIN_URL=${ORIGIN_URL:-http://localhost:3002}
      - PASSKEY_RP_ID=${PASSKEY_RP_ID:-localhost}
      - PASSKEY_RP_NAME=${PASSKEY_RP_NAME:-LLMGateway}
      - VITE_POSTHOG_KEY=${VITE_POSTHOG_KEY}
      - VITE_POSTHOG_HOST=${VITE_POSTHOG_HOST}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
    ports:
      - "${API_PORT:-4002}:80"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:80/",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - llmgateway-network

  # UI Service (Static files served via Nginx)
  ui:
    build:
      context: ..
      dockerfile: infra/split.dockerfile
      target: ui
    container_name: llmgateway-ui
    restart: unless-stopped
    ports:
      - "${UI_PORT:-3002}:80"
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:80/",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - llmgateway-network
    environment:
      - VITE_DOCS_URL=${VITE_DOCS_URL:-http://localhost:3005}
      - VITE_API_URL=${VITE_API_URL:-http://localhost:4002}
      - VITE_POSTHOG_HOST=${VITE_POSTHOG_HOST}
      - VITE_POSTHOG_KEY=${VITE_POSTHOG_KEY}
      - VITE_API=${VITE_API:-http://localhost:4002}

  # Docs Service (Static files served via Nginx)
  docs:
    build:
      context: ..
      dockerfile: infra/split.dockerfile
      target: docs
    container_name: llmgateway-docs
    restart: unless-stopped
    ports:
      - "${DOCS_PORT:-3005}:80"
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:80/",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - llmgateway-network
    environment:
      - VITE_DOCS_URL=${VITE_DOCS_URL:-http://localhost:3005}
      - NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY}
      - NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST}

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  llmgateway-network:
    driver: bridge
