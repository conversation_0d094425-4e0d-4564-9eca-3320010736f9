[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:postgresql]
command=/usr/bin/postgres -D /var/lib/postgresql/data
user=postgres
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
environment=PGDATA="/var/lib/postgresql/data"

[program:redis]
command=/usr/bin/redis-server --protected-mode no --bind 0.0.0.0 --port 6379 --appendonly no --save "" --dir /var/lib/redis
user=redis
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0


[program:ui]
command=/bin/pnpm start
directory=/app/services/ui
user=node
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
environment=PORT="3002",NODE_ENV="production"

[program:docs]
command=/bin/pnpm start
directory=/app/services/docs
user=node
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
environment=PORT="3005",NODE_ENV="production"


[program:api]
command=/bin/pnpm start
directory=/app/services/api
user=node
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
environment=PORT="4002",NODE_ENV="production"

[program:gateway]
command=node dist/serve.js
directory=/app/services/gateway
user=node
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
environment=PORT="4001",NODE_ENV="production"
