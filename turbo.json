{"$schema": "https://turbo.build/schema.json", "remoteCache": {"teamSlug": "ducktors", "apiUrl": "https://do-trrc.ducktors.dev"}, "dangerouslyDisablePackageManagerCheck": true, "globalDependencies": ["tsup.config.ts", "package.json", "pnpm-lock.yaml", "pnpm-workspace.yaml", "tsup.config.ts", "tsconfig.json", "turbo.json"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", ".source/**", ".vercel/**", ".output/**", ".vinxi/**", "out/**", "openapi.json"]}, "generate": {"dependsOn": ["^generate"], "cache": false}, "dev": {"cache": false}, "lint": {}, "test": {}}}